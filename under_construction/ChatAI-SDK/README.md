# ChatAI SDK Service 🚀

Express SDK service for ChatAI with LlamaIndex and OpenRouter integration. This service provides a complete chat interface with document-based RAG (Retrieval-Augmented Generation) capabilities.

## 🏗️ Architecture

```
User Request → Express SDK → [Session Check] → [User-Service API] → LlamaIndex Cloud → OpenRouter → Streaming Response
```

### Key Features

- **Session-based Caching**: Minimizes User-Service calls after first request
- **Direct LlamaIndex Integration**: Fast document retrieval without middleware
- **Streaming Responses**: Real-time chat with Server-Sent Events
- **Multi-document RAG**: Parallel retrieval from multiple documents
- **Rate Limiting**: Prevents abuse and ensures fair usage
- **Comprehensive Error Handling**: Graceful fallbacks and user-friendly messages

## 🚀 Quick Start

### 1. Installation

```bash
cd ChatAI-SDK
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
# Edit .env with your API keys
```

Required environment variables:

```env
LLAMA_CLOUD_API_KEY=llx-your-llamaindex-api-key
OPENROUTER_API_KEY=sk-or-your-openrouter-api-key
USER_SERVICE_URL=http://localhost:3000
```

### 3. Start the Service

```bash
# Development
npm run dev

# Production
npm start
```

The service will start on `http://localhost:3001`

## 📡 API Endpoints

### Chat with Documents

```http
POST /chat/:appId?apiKey=your-api-key
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "query": "What is the main topic of the documents?",
  "sessionId": "optional-session-id",
  "stream": true,
  "includeHistory": false
}
```

**Note**: API key validation is temporarily disabled for development. Kong integration will be enabled in production.

**Response (Streaming):**

```
data: {"type":"session","sessionId":"uuid","timestamp":"2024-01-01T00:00:00.000Z"}
data: {"type":"content","content":"The main topic"}
data: {"type":"content","content":" of the documents is..."}
data: {"type":"done","timestamp":"2024-01-01T00:00:01.000Z"}
```

### Session Management

```http
# Get session info
GET /session/:sessionId

# Invalidate session
DELETE /session/:sessionId

# Get service stats
GET /stats

# Health check
GET /health
```

## 🔄 Complete Flow

### First Chat Request

1. **Session Check**: Create new session for appId
2. **User-Service Call**: Fetch documents with retriever IDs
3. **Cache Documents**: Store in session cache (15 min TTL)
4. **LlamaIndex Retrieval**: Parallel calls to multiple retrievers
5. **Context Building**: Combine retrieved content
6. **OpenRouter LLM**: Generate response with context
7. **Stream Response**: Real-time response to user

### Subsequent Requests

1. **Session Check**: Use cached session
2. **Skip User-Service**: Use cached documents ⚡
3. **Direct LlamaIndex**: Fast retrieval
4. **Stream Response**: Optimized flow

## 🎯 Performance Benefits

| Scenario         | User-Service Calls | Latency   | Cache Hit Rate |
| ---------------- | ------------------ | --------- | -------------- |
| First Chat       | 1                  | 300-500ms | 0%             |
| Subsequent Chats | 0                  | 100-200ms | ~90%           |

## 🔧 Configuration

### Cache Settings

```javascript
cache: {
  ttlMinutes: 15,        // Session TTL
  maxSessions: 1000,     // Memory limit
  cleanupIntervalMinutes: 5  // Cleanup frequency
}
```

### Rate Limiting

```javascript
rateLimit: {
  windowMinutes: 15,     // Rate limit window
  maxRequests: 100,      // Max requests per window
}

chatRateLimit: {
  windowMinutes: 1,      // Chat-specific window
  maxRequests: 20        // Max chat requests per minute
}
```

## 🛠️ Development

### Project Structure

```
src/
├── config/           # Configuration management
├── controllers/      # Request handlers
├── middleware/       # Validation, rate limiting
├── routes/          # API routes
├── services/        # Business logic
│   ├── userService.js      # User-Service integration
│   ├── llamaIndexService.js # LlamaIndex Cloud API
│   ├── openRouterService.js # OpenRouter LLM API
│   └── cacheService.js     # Session management
└── server.js        # Express app setup
```

### Testing

```bash
# Run tests
npm test

# Test health endpoint
curl http://localhost:3001/health

# Test chat endpoint
curl -X POST http://localhost:3001/chat/your-app-id \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"query": "Hello, what can you tell me about the documents?"}'
```

## 🔐 Security Features

- **JWT Authentication**: Required for all chat endpoints
- **Rate Limiting**: IP and user-based limits
- **Input Validation**: Comprehensive request validation
- **CORS Protection**: Configurable origin whitelist
- **Helmet Security**: Standard security headers
- **Request Size Limits**: Prevents large payload attacks

## 📊 Monitoring

### Health Check Response

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "services": {
    "llamaIndex": true,
    "openRouter": true,
    "cache": true
  },
  "cache": {
    "totalSessions": 45,
    "totalApps": 12,
    "maxSessions": 1000,
    "ttlMinutes": 15
  }
}
```

### Stats Endpoint

```json
{
  "error": false,
  "stats": {
    "totalSessions": 45,
    "totalApps": 12,
    "maxSessions": 1000,
    "ttlMinutes": 15,
    "cleanupIntervalMinutes": 5
  }
}
```

## 🚀 Deployment

### Docker (Recommended)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY src/ ./src/
EXPOSE 3001
CMD ["npm", "start"]
```

### Environment Variables for Production

```env
NODE_ENV=production
PORT=3001
LLAMA_CLOUD_API_KEY=your-production-key
OPENROUTER_API_KEY=your-production-key
USER_SERVICE_URL=https://your-user-service.com
CACHE_TTL_MINUTES=15
MAX_SESSIONS=5000
RATE_LIMIT_MAX_REQUESTS=1000
```

## 🤝 Integration with User-Service

The SDK integrates with User-Service through these endpoints:

1. **Document Fetching**: `GET /users/app/chatai/get-documents?appId=xxx`
2. **App Validation**: `GET /users/app/chatai/get-single-chatai?appId=xxx`

Both endpoints require JWT authentication and return document metadata including retriever IDs.

## 📝 License

MIT License - see LICENSE file for details.
