/**
 * ChatAI SDK Usage Example
 * 
 * This example demonstrates how to use the ChatAI SDK service
 * for document-based chat with streaming responses.
 */

const fetch = require('node-fetch');

// Configuration
const SDK_BASE_URL = 'http://localhost:3001';
const APP_ID = 'your-app-id-here'; // Replace with actual app ID
const JWT_TOKEN = 'your-jwt-token-here'; // Replace with actual JWT token

/**
 * Example 1: Simple Chat Request (Non-streaming)
 */
async function simpleChatExample() {
  console.log('🔄 Simple Chat Example');
  console.log('======================');

  try {
    const response = await fetch(`${SDK_BASE_URL}/chat/${APP_ID}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'What is the main topic of the uploaded documents?',
        stream: false // Non-streaming response
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    console.log('✅ Response received:');
    console.log('Session ID:', result.sessionId);
    console.log('Documents Used:', result.documentsUsed);
    console.log('Context Length:', result.contextLength);
    console.log('Response:', result.response);

  } catch (error) {
    console.error('❌ Simple chat error:', error.message);
  }
}

/**
 * Example 2: Streaming Chat Request
 */
async function streamingChatExample() {
  console.log('\n🔄 Streaming Chat Example');
  console.log('==========================');

  try {
    const response = await fetch(`${SDK_BASE_URL}/chat/${APP_ID}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'Can you summarize the key points from all the documents?',
        stream: true // Enable streaming
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    console.log('✅ Streaming response started...');
    
    // Parse Server-Sent Events
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let sessionId = null;
    let fullResponse = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n').filter(line => line.trim());

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          try {
            const parsed = JSON.parse(data);
            
            switch (parsed.type) {
              case 'session':
                sessionId = parsed.sessionId;
                console.log(`📋 Session started: ${sessionId}`);
                break;
                
              case 'content':
                process.stdout.write(parsed.content);
                fullResponse += parsed.content;
                break;
                
              case 'done':
                console.log(`\n✅ Streaming completed at ${parsed.timestamp}`);
                break;
                
              case 'error':
                console.error(`❌ Streaming error: ${parsed.message}`);
                break;
            }
          } catch (parseError) {
            // Skip invalid JSON
          }
        }
      }
    }

    console.log(`\n📊 Full response length: ${fullResponse.length} characters`);
    console.log(`📋 Session ID for future use: ${sessionId}`);

  } catch (error) {
    console.error('❌ Streaming chat error:', error.message);
  }
}

/**
 * Example 3: Session Management
 */
async function sessionManagementExample() {
  console.log('\n🔄 Session Management Example');
  console.log('==============================');

  try {
    // First, create a session with a chat
    console.log('1. Creating session with initial chat...');
    const chatResponse = await fetch(`${SDK_BASE_URL}/chat/${APP_ID}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'Hello, what documents do you have access to?',
        stream: false
      })
    });

    const chatResult = await chatResponse.json();
    const sessionId = chatResult.sessionId;
    console.log(`✅ Session created: ${sessionId}`);

    // Get session info
    console.log('\n2. Getting session information...');
    const sessionResponse = await fetch(`${SDK_BASE_URL}/session/${sessionId}`);
    const sessionInfo = await sessionResponse.json();
    
    console.log('✅ Session info:');
    console.log('   App ID:', sessionInfo.session.appId);
    console.log('   Documents Count:', sessionInfo.session.documentsCount);
    console.log('   Access Count:', sessionInfo.session.accessCount);
    console.log('   Last Accessed:', new Date(sessionInfo.session.lastAccessed).toLocaleString());

    // Use the same session for another chat
    console.log('\n3. Using existing session for another chat...');
    const secondChatResponse = await fetch(`${SDK_BASE_URL}/chat/${APP_ID}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'What are the main themes across all documents?',
        sessionId: sessionId, // Reuse session
        stream: false
      })
    });

    const secondChatResult = await secondChatResponse.json();
    console.log('✅ Second chat completed using cached session');
    console.log('   Same session ID:', secondChatResult.sessionId === sessionId);

    // Invalidate session
    console.log('\n4. Invalidating session...');
    const deleteResponse = await fetch(`${SDK_BASE_URL}/session/${sessionId}`, {
      method: 'DELETE'
    });

    const deleteResult = await deleteResponse.json();
    console.log('✅ Session invalidated:', deleteResult.message);

  } catch (error) {
    console.error('❌ Session management error:', error.message);
  }
}

/**
 * Example 4: Service Health and Stats
 */
async function healthAndStatsExample() {
  console.log('\n🔄 Health and Stats Example');
  console.log('============================');

  try {
    // Health check
    console.log('1. Checking service health...');
    const healthResponse = await fetch(`${SDK_BASE_URL}/health`);
    const health = await healthResponse.json();
    
    console.log('✅ Service health:');
    console.log('   Status:', health.status);
    console.log('   LlamaIndex:', health.services.llamaIndex ? '✅' : '❌');
    console.log('   OpenRouter:', health.services.openRouter ? '✅' : '❌');
    console.log('   Cache:', health.services.cache ? '✅' : '❌');

    // Service stats
    console.log('\n2. Getting service statistics...');
    const statsResponse = await fetch(`${SDK_BASE_URL}/stats`);
    const stats = await statsResponse.json();
    
    console.log('✅ Service stats:');
    console.log('   Total Sessions:', stats.stats.totalSessions);
    console.log('   Total Apps:', stats.stats.totalApps);
    console.log('   Max Sessions:', stats.stats.maxSessions);
    console.log('   Cache TTL:', stats.stats.ttlMinutes, 'minutes');

  } catch (error) {
    console.error('❌ Health and stats error:', error.message);
  }
}

/**
 * Example 5: Error Handling
 */
async function errorHandlingExample() {
  console.log('\n🔄 Error Handling Example');
  console.log('==========================');

  // Test with invalid app ID
  try {
    console.log('1. Testing with invalid app ID...');
    const response = await fetch(`${SDK_BASE_URL}/chat/invalid-app-id`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'Test query'
      })
    });

    const result = await response.json();
    console.log('❌ Expected error:', result.message);

  } catch (error) {
    console.error('❌ Error handling test:', error.message);
  }

  // Test without authorization
  try {
    console.log('\n2. Testing without authorization...');
    const response = await fetch(`${SDK_BASE_URL}/chat/${APP_ID}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'Test query'
      })
    });

    const result = await response.json();
    console.log('❌ Expected error:', result.message);

  } catch (error) {
    console.error('❌ Error handling test:', error.message);
  }
}

/**
 * Run all examples
 */
async function runAllExamples() {
  console.log('🚀 ChatAI SDK Examples');
  console.log('=======================\n');

  // Check if configuration is set
  if (APP_ID === 'your-app-id-here' || JWT_TOKEN === 'your-jwt-token-here') {
    console.log('⚠️  Please update APP_ID and JWT_TOKEN in this file before running examples');
    return;
  }

  await simpleChatExample();
  await streamingChatExample();
  await sessionManagementExample();
  await healthAndStatsExample();
  await errorHandlingExample();

  console.log('\n✅ All examples completed!');
}

// Run examples if this file is executed directly
if (require.main === module) {
  runAllExamples().catch(console.error);
}

module.exports = {
  simpleChatExample,
  streamingChatExample,
  sessionManagementExample,
  healthAndStatsExample,
  errorHandlingExample
};
