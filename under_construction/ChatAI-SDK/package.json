{"name": "chatai-sdk-service", "version": "1.0.0", "description": "Express SDK service for ChatAI with LlamaIndex and OpenRouter integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "node-fetch": "^3.3.2", "uuid": "^9.0.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "keywords": ["chatai", "sdk", "llamaindex", "openrouter", "rag", "express"], "author": "ChatAI Team", "license": "MIT"}