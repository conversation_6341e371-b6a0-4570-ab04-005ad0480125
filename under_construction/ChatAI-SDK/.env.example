# Server Configuration
PORT=3001
NODE_ENV=development

# User Service Configuration
USER_SERVICE_URL=http://localhost:3000
USER_SERVICE_API_KEY=your-user-service-api-key

# LlamaIndex Cloud Configuration
LLAMA_CLOUD_API_KEY=llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp

# OpenRouter Configuration
OPENROUTER_API_KEY=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


# Cache Configuration
CACHE_TTL_MINUTES=15
MAX_SESSIONS=1000
CLEANUP_INTERVAL_MINUTES=5

# Rate Limiting
RATE_LIMIT_WINDOW_MINUTES=15
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
