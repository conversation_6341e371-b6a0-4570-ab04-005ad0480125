const userService = require('../services/userService');
const llamaIndexService = require('../services/llamaIndexService');
const openRouterService = require('../services/openRouterService');
const cacheService = require('../services/cacheService');

class ChatController {
  /**
   * Main chat endpoint with complete flow
   * POST /chat/:appId
   */
  async chat(req, res) {
    const { appId } = req.params;
    const { query, sessionId, stream = true, includeHistory = false } = req.body;
    const { apiKey } = req.query; // Application API key from URL
    const authToken = req.headers.authorization?.replace('Bearer ', '');

    try {
      // Validation
      if (!appId || !query) {
        return res.status(400).json({
          error: true,
          message: 'appId and query are required'
        });
      }

      // TODO: Enable apiKey validation when Kong is ready
      // if (!apiKey) {
      //   return res.status(400).json({
      //     error: true,
      //     message: 'Application API key is required in query parameters'
      //   });
      // }

      if (!authToken) {
        return res.status(401).json({
          error: true,
          message: 'Authorization token is required'
        });
      }

      console.log(`💬 Chat request for appId: ${appId}, query: "${query.substring(0, 50)}..."`);

      // Step 1: Validate applicationId and check if ChatAI service exists
      console.log(`🔍 Validating applicationId: ${appId}`);
      const isValidApp = await userService.validateAppId(appId, authToken);

      if (!isValidApp) {
        return res.status(404).json({
          error: true,
          message: 'Application not found or no ChatAI service configured for this application'
        });
      }

      console.log(`✅ Application validated: ${appId}`);

      // Step 2: Get or create session
      const currentSessionId = cacheService.getOrCreateSession(appId, sessionId);

      // Step 3: Get documents (cached or fetch from User-Service)
      const documents = await this.getDocumentsForChat(currentSessionId, appId, authToken);

      if (!documents || documents.length === 0) {
        return res.status(404).json({
          error: true,
          message: 'No ready documents found for this application'
        });
      }

      // Step 4: Retrieve context from LlamaIndex
      const context = await llamaIndexService.retrieveFromMultipleDocuments(documents, query);

      // Step 5: Generate response
      if (stream) {
        await this.streamChatResponse(res, query, context, currentSessionId);
      } else {
        const response = await openRouterService.generateResponse(query, context);

        res.json({
          error: false,
          sessionId: currentSessionId,
          response,
          documentsUsed: documents.length,
          contextLength: context.length
        });
      }

    } catch (error) {
      console.error('❌ Chat error:', error.message);

      if (!res.headersSent) {
        res.status(500).json({
          error: true,
          message: 'Internal server error during chat processing'
        });
      }
    }
  }

  /**
   * Get documents for chat (with caching)
   */
  async getDocumentsForChat(sessionId, appId, authToken) {
    // Try to get from cache first
    const cachedSession = cacheService.getCachedDocuments(sessionId);

    if (cachedSession && cachedSession.appId === appId) {
      console.log(`📋 Using cached documents for session: ${sessionId}`);
      return cachedSession.documents;
    }

    // Fetch from User-Service
    console.log(`🔄 Fetching fresh documents from User-Service for appId: ${appId}`);
    const documents = await userService.getDocuments(appId, authToken);

    // Cache the documents
    cacheService.cacheDocuments(sessionId, appId, documents, authToken);

    return documents;
  }

  /**
   * Stream chat response
   */
  async streamChatResponse(res, query, context, sessionId) {
    try {
      // Set headers for streaming
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');

      // Send initial session info
      res.write(`data: ${JSON.stringify({
        type: 'session',
        sessionId,
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Stream response from OpenRouter
      const streamGenerator = openRouterService.generateStreamingResponse(query, context);

      for await (const chunk of streamGenerator) {
        res.write(`data: ${JSON.stringify({
          type: 'content',
          content: chunk
        })}\n\n`);
      }

      // Send completion signal
      res.write(`data: ${JSON.stringify({
        type: 'done',
        timestamp: new Date().toISOString()
      })}\n\n`);

      res.end();

    } catch (error) {
      console.error('❌ Streaming error:', error.message);

      if (!res.headersSent) {
        res.write(`data: ${JSON.stringify({
          type: 'error',
          message: 'Error during response generation'
        })}\n\n`);
      }

      res.end();
    }
  }

  /**
   * Get session info
   * GET /session/:sessionId
   */
  async getSession(req, res) {
    const { sessionId } = req.params;

    try {
      const session = cacheService.getCachedDocuments(sessionId);

      if (!session) {
        return res.status(404).json({
          error: true,
          message: 'Session not found or expired'
        });
      }

      res.json({
        error: false,
        session: {
          sessionId: session.sessionId,
          appId: session.appId,
          documentsCount: session.documents.length,
          timestamp: session.timestamp,
          lastAccessed: session.lastAccessed,
          accessCount: session.accessCount
        }
      });

    } catch (error) {
      console.error('❌ Get session error:', error.message);
      res.status(500).json({
        error: true,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Invalidate session
   * DELETE /session/:sessionId
   */
  async invalidateSession(req, res) {
    const { sessionId } = req.params;

    try {
      cacheService.invalidateSession(sessionId);

      res.json({
        error: false,
        message: 'Session invalidated successfully'
      });

    } catch (error) {
      console.error('❌ Invalidate session error:', error.message);
      res.status(500).json({
        error: true,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Get cache statistics
   * GET /stats
   */
  async getStats(req, res) {
    try {
      const stats = cacheService.getStats();

      res.json({
        error: false,
        stats
      });

    } catch (error) {
      console.error('❌ Get stats error:', error.message);
      res.status(500).json({
        error: true,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Health check endpoint
   * GET /health
   */
  async healthCheck(req, res) {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          llamaIndex: llamaIndexService.isConfigured,
          openRouter: openRouterService.isConfigured,
          cache: true
        },
        cache: cacheService.getStats()
      };

      res.json(health);

    } catch (error) {
      console.error('❌ Health check error:', error.message);
      res.status(500).json({
        status: 'unhealthy',
        error: error.message
      });
    }
  }
}

module.exports = new ChatController();
