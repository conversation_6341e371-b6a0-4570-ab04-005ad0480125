const express = require('express');
const chatController = require('../controllers/chatController');
const { validateChatRequest, validateSessionId } = require('../middleware/validation');
const rateLimit = require('../middleware/rateLimit');

const router = express.Router();

// Apply rate limiting to all routes
router.use(rateLimit);

// Chat routes
router.post('/chat/:appId', validateChatRequest, chatController.chat.bind(chatController));

// Session management routes
router.get('/session/:sessionId', validateSessionId, chatController.getSession.bind(chatController));
router.delete('/session/:sessionId', validateSessionId, chatController.invalidateSession.bind(chatController));

// Utility routes
router.get('/stats', chatController.getStats.bind(chatController));
router.get('/health', chatController.healthCheck.bind(chatController));

// API documentation endpoint
router.get('/', (req, res) => {
  res.json({
    name: 'ChatAI SDK Service',
    version: '1.0.0',
    description: 'Express SDK service for ChatAI with LlamaIndex and OpenRouter integration',
    endpoints: {
      'POST /chat/:appId': {
        description: 'Chat with documents for a specific app',
        parameters: {
          appId: 'Application ID (path parameter)',
          query: 'User query (body)',
          sessionId: 'Optional session ID (body)',
          stream: 'Enable streaming response (body, default: true)',
          includeHistory: 'Include chat history (body, default: false)'
        },
        headers: {
          'Authorization': 'Bearer <jwt-token>'
        }
      },
      'GET /session/:sessionId': {
        description: 'Get session information',
        parameters: {
          sessionId: 'Session ID (path parameter)'
        }
      },
      'DELETE /session/:sessionId': {
        description: 'Invalidate a session',
        parameters: {
          sessionId: 'Session ID (path parameter)'
        }
      },
      'GET /stats': {
        description: 'Get cache and service statistics'
      },
      'GET /health': {
        description: 'Health check endpoint'
      }
    },
    examples: {
      chat: {
        url: 'POST /chat/your-app-id',
        headers: {
          'Authorization': 'Bearer your-jwt-token',
          'Content-Type': 'application/json'
        },
        body: {
          query: 'What is the main topic of the documents?',
          sessionId: 'optional-session-id',
          stream: true
        }
      },
      streaming_response: {
        description: 'Server-Sent Events format',
        events: [
          'data: {"type":"session","sessionId":"uuid","timestamp":"2024-01-01T00:00:00.000Z"}',
          'data: {"type":"content","content":"The main topic"}',
          'data: {"type":"content","content":" of the documents is..."}',
          'data: {"type":"done","timestamp":"2024-01-01T00:00:01.000Z"}'
        ]
      }
    }
  });
});

module.exports = router;
