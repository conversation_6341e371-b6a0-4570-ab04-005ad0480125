const fetch = require('node-fetch');
const config = require('../config');

class UserService {
  constructor() {
    this.baseUrl = config.userService.url;
    this.apiKey = config.userService.apiKey;
  }

  /**
   * Fetch documents for a given appId
   * @param {string} appId - Application ID
   * @param {string} authToken - JWT token for authentication
   * @returns {Promise<Array>} Array of documents with retriever IDs
   */
  async getDocuments(appId, authToken) {
    try {
      console.log(`📋 Fetching documents for appId: ${appId}`);

      const response = await fetch(
        `${this.baseUrl}/users/app/chatai/get-documents?appId=${appId}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`User Service error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.message || 'Failed to fetch documents');
      }

      // Filter ready documents with valid retriever IDs
      const readyDocuments = (result.result || []).filter(doc => 
        doc.status === 'ready' && 
        doc.indexId && 
        !doc.indexId.startsWith('fallback-')
      );

      console.log(`✅ Found ${readyDocuments.length} ready documents`);
      return readyDocuments;

    } catch (error) {
      console.error('❌ UserService.getDocuments error:', error.message);
      throw error;
    }
  }

  /**
   * Validate if appId exists and user has access
   * @param {string} appId - Application ID
   * @param {string} authToken - JWT token for authentication
   * @returns {Promise<boolean>} True if valid
   */
  async validateAppId(appId, authToken) {
    try {
      console.log(`🔍 Validating appId: ${appId}`);

      const response = await fetch(
        `${this.baseUrl}/users/app/chatai/get-single-chatai?appId=${appId}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        return false;
      }

      const result = await response.json();
      return !result.error;

    } catch (error) {
      console.error('❌ UserService.validateAppId error:', error.message);
      return false;
    }
  }
}

module.exports = new UserService();
