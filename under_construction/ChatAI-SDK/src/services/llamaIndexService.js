const fetch = require('node-fetch');
const config = require('../config');

class LlamaIndexService {
  constructor() {
    this.apiKey = config.llamaIndex.apiKey;
    this.baseUrl = config.llamaIndex.baseUrl;
    this.isConfigured = !!this.apiKey;
    
    if (!this.isConfigured) {
      console.warn('⚠️  LLAMA_CLOUD_API_KEY not configured. Vector retrieval will be disabled.');
    } else {
      console.log('✅ LlamaIndexService initialized successfully');
    }
  }

  checkConfiguration() {
    if (!this.isConfigured) {
      throw new Error('LlamaIndex service is not configured. Please set LLAMA_CLOUD_API_KEY.');
    }
  }

  /**
   * Retrieve relevant chunks from vector index using retriever
   * @param {string} retrieverId - LlamaIndex retriever ID
   * @param {string} query - Search query
   * @param {number} topK - Number of top results to return
   * @returns {Promise<Object>} Retrieval result with nodes
   */
  async retrieve(retrieverId, query, topK = 5) {
    this.checkConfiguration();

    try {
      console.log(`🔍 Retrieving from retriever ${retrieverId} for query: "${query.substring(0, 50)}..."`);

      const payload = {
        query,
        top_k: topK,
      };

      const response = await fetch(`${this.baseUrl}/retrievers/${retrieverId}/retrieve`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ LlamaIndex retrieve error: ${response.status} - ${errorText}`);
        throw new Error(`Retrieval failed: ${response.status}`);
      }

      const result = await response.json();
      console.log(`✅ Retrieved ${result.nodes?.length || 0} relevant chunks`);

      return result;
    } catch (error) {
      console.error(`❌ LlamaIndex retrieve error:`, error.message);
      throw error;
    }
  }

  /**
   * Retrieve context from multiple documents in parallel
   * @param {Array} documents - Array of document objects with indexId
   * @param {string} query - Search query
   * @param {number} topK - Number of top results per document
   * @returns {Promise<string>} Combined context from all documents
   */
  async retrieveFromMultipleDocuments(documents, query, topK = 5) {
    this.checkConfiguration();

    if (!documents || documents.length === 0) {
      return '';
    }

    console.log(`🔍 Retrieving from ${documents.length} documents in parallel`);

    // Create parallel retrieval promises
    const retrievalPromises = documents.map(async (doc) => {
      try {
        const result = await this.retrieve(doc.indexId, query, topK);
        return {
          filename: doc.filename,
          documentId: doc.id,
          nodes: result.nodes || [],
          success: true
        };
      } catch (error) {
        console.warn(`⚠️ Failed to retrieve from ${doc.filename}:`, error.message);
        return {
          filename: doc.filename,
          documentId: doc.id,
          nodes: [],
          success: false,
          error: error.message
        };
      }
    });

    // Wait for all retrievals to complete
    const results = await Promise.all(retrievalPromises);

    // Combine context from successful retrievals
    let context = '';
    let successfulRetrievals = 0;

    results.forEach(({ filename, nodes, success }) => {
      if (success && nodes.length > 0) {
        context += `--- ${filename} ---\n`;
        context += nodes.map(node => node.text).join('\n\n');
        context += '\n\n';
        successfulRetrievals++;
      }
    });

    console.log(`✅ Successfully retrieved context from ${successfulRetrievals}/${documents.length} documents`);
    return context;
  }
}

module.exports = new LlamaIndexService();
